#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TEST SIMPLE DU PRÉDICTEUR ULTRA-OPTIMISÉ
========================================
Test basique pour identifier les problèmes
"""

import json
import sys
import traceback

def test_chargement_donnees():
    """Test de chargement des données"""
    print("🔍 Test de chargement des données...")
    
    try:
        with open("dataset_baccarat_lupasco_20250629_165801.json", 'r', encoding='utf-8') as f:
            donnees = json.load(f)
        
        print(f"✅ Fichier JSON chargé")
        print(f"✅ Nombre de parties: {len(donnees['parties'])}")
        
        # Test extraction 10 premières parties
        mains_test = []
        for i, partie in enumerate(donnees['parties'][:10]):
            if 'mains' in partie:
                for main in partie['mains']:
                    if (main.get('index1') is not None and 
                        main.get('index2') and 
                        main.get('index3') and 
                        main.get('index5')):
                        mains_test.append(main)
        
        print(f"✅ {len(mains_test)} mains extraites des 10 premières parties")
        return True
        
    except Exception as e:
        print(f"❌ Erreur chargement: {e}")
        traceback.print_exc()
        return False

def test_import_predicteur():
    """Test d'import du prédicteur"""
    print("\n🔍 Test d'import du prédicteur...")
    
    try:
        from predicteur_ultra_optimise import PredicteurUltraOptimise
        print("✅ Import réussi")
        
        # Test initialisation
        config_simple = {
            'fenetre_historique': 100,
            'fenetre_sequences': 10,
            'seuil_patterns': 10,
            'profondeur_analyse': 5,
            'ensemble_size': 2,
            'optimisation_hyperparams': False  # Désactivé pour test
        }
        
        predicteur = PredicteurUltraOptimise(config_simple)
        print("✅ Initialisation réussie")
        return True
        
    except Exception as e:
        print(f"❌ Erreur import: {e}")
        traceback.print_exc()
        return False

def test_prediction_simple():
    """Test de prédiction simple"""
    print("\n🔍 Test de prédiction simple...")
    
    try:
        from predicteur_ultra_optimise import PredicteurUltraOptimise
        
        config_minimal = {
            'fenetre_historique': 50,
            'fenetre_sequences': 5,
            'seuil_patterns': 20,
            'profondeur_analyse': 3,
            'ensemble_size': 1,
            'optimisation_hyperparams': False
        }
        
        predicteur = PredicteurUltraOptimise(config_minimal)
        
        # Test avec historique minimal
        historique_test = [
            {'index1': 0, 'index2': 'A', 'index3': 'BANKER', 'index5': '0_A_BANKER'},
            {'index1': 1, 'index2': 'B', 'index3': 'PLAYER', 'index5': '1_B_PLAYER'},
        ]
        
        prediction = predicteur.predire_ultra_optimise(historique_test)
        print(f"✅ Prédiction: {prediction}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur prédiction: {e}")
        traceback.print_exc()
        return False

def main():
    """Test principal"""
    print("🚀 TESTS SIMPLES DU PRÉDICTEUR")
    print("=" * 40)
    
    # Test 1: Chargement données
    if not test_chargement_donnees():
        print("❌ Échec test chargement")
        return
    
    # Test 2: Import prédicteur
    if not test_import_predicteur():
        print("❌ Échec test import")
        return
    
    # Test 3: Prédiction simple
    if not test_prediction_simple():
        print("❌ Échec test prédiction")
        return
    
    print("\n🎉 TOUS LES TESTS SIMPLES RÉUSSIS!")
    print("✅ Le prédicteur fonctionne correctement")

if __name__ == "__main__":
    main()

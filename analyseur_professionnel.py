#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR PROFESSIONNEL BACCARAT - PHASE 4
==========================================
Programme d'analyse statistique complète pour fichiers partie.txt
Intègre tous les algorithmes mathématiques extraits des .tex/.md

Auteur: Augment Agent
Date: 2025-06-30
Objectif: Examen de ma vie - Analyseur professionnel
"""

import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import chi2_contingency, kstest, normaltest
from collections import Counter, defaultdict
import math
from typing import Dict, List, Tuple, Any
import warnings
warnings.filterwarnings('ignore')

# Configuration matplotlib pour affichage français
plt.rcParams['font.size'] = 10
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['axes.grid'] = True
plt.rcParams['grid.alpha'] = 0.3

class AnalyseurProfessionnelBaccarat:
    """
    Analyseur statistique professionnel pour données baccarat
    Implémente tous les algorithmes mathématiques avancés
    """
    
    def __init__(self):
        self.donnees = None
        self.mains = []
        self.statistiques = {}
        self.matrices_transition = {}
        self.entropies = {}
        self.tests_statistiques = {}
        
    def charger_donnees(self, fichier_path: str):
        """
        Charge les données depuis un fichier partie.txt
        """
        try:
            with open(fichier_path, 'r', encoding='utf-8') as f:
                self.donnees = json.load(f)
            
            # Extraction des mains
            self.mains = []
            for i, partie in enumerate(self.donnees['parties']):
                # Vérifier si la partie a des mains ou est directement une main
                if 'mains' in partie:
                    # Structure avec parties contenant des mains
                    for main in partie['mains']:
                        # Ignorer les mains vides ou nulles
                        if (main.get('index1') is not None and
                            main.get('index2') and
                            main.get('index3') and
                            main.get('index5')):
                            self.mains.append({
                                'partie_id': partie.get('partie_number', i),
                                'main_id': main.get('main_number', main.get('main', 0)),
                                'index1': main['index1'],
                                'index2': main['index2'],
                                'index3': main['index3'],
                                'index5': main['index5']
                            })
                else:
                    # Structure directe (chaque élément est une main)
                    if (partie.get('index1') is not None and
                        partie.get('index2') and
                        partie.get('index3') and
                        partie.get('index5')):
                        self.mains.append({
                            'partie_id': i,
                            'main_id': partie.get('main_number', partie.get('main', i)),
                            'index1': partie['index1'],
                            'index2': partie['index2'],
                            'index3': partie['index3'],
                            'index5': partie['index5']
                        })
            
            print(f"✅ Données chargées: {len(self.mains)} mains analysées")
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement: {e}")
            return False
    
    def calculer_statistiques_descriptives(self):
        """
        Calcule toutes les statistiques descriptives avancées
        """
        print("\n📊 CALCUL STATISTIQUES DESCRIPTIVES")
        print("=" * 50)
        
        # Conversion en DataFrame pour faciliter l'analyse
        df = pd.DataFrame(self.mains)
        
        # 1. STATISTIQUES INDEX1 (SYNC/DESYNC)
        index1_counts = df['index1'].value_counts()
        total_mains = len(df)
        
        self.statistiques['index1'] = {
            'distribution': index1_counts.to_dict(),
            'proportions': (index1_counts / total_mains).to_dict(),
            'entropie_shannon': self._calculer_entropie_shannon(index1_counts.values),
            'test_uniformite': self._test_uniformite(index1_counts.values)
        }
        
        # 2. STATISTIQUES INDEX2 (A/B/C)
        index2_counts = df['index2'].value_counts()
        self.statistiques['index2'] = {
            'distribution': index2_counts.to_dict(),
            'proportions': (index2_counts / total_mains).to_dict(),
            'entropie_shannon': self._calculer_entropie_shannon(index2_counts.values),
            'test_uniformite': self._test_uniformite(index2_counts.values)
        }
        
        # 3. STATISTIQUES INDEX3 (BANKER/PLAYER/TIE)
        index3_counts = df['index3'].value_counts()
        self.statistiques['index3'] = {
            'distribution': index3_counts.to_dict(),
            'proportions': (index3_counts / total_mains).to_dict(),
            'entropie_shannon': self._calculer_entropie_shannon(index3_counts.values),
            'test_uniformite': self._test_uniformite(index3_counts.values),
            'baseline_banker': index3_counts.get('BANKER', 0) / total_mains
        }
        
        # 4. STATISTIQUES INDEX5 (18 valeurs)
        index5_counts = df['index5'].value_counts()
        self.statistiques['index5'] = {
            'distribution': index5_counts.to_dict(),
            'proportions': (index5_counts / total_mains).to_dict(),
            'entropie_shannon': self._calculer_entropie_shannon(index5_counts.values),
            'valeurs_uniques': len(index5_counts),
            'entropie_maximale': math.log2(len(index5_counts))
        }
        
        # 5. MOMENTS STATISTIQUES (pour INDEX numérique)
        index1_numeric = df['index1'].astype(float)
        self.statistiques['moments'] = {
            'moyenne_index1': float(np.mean(index1_numeric)),
            'variance_index1': float(np.var(index1_numeric)),
            'skewness_index1': float(stats.skew(index1_numeric)),
            'kurtosis_index1': float(stats.kurtosis(index1_numeric)),
            'ecart_type_index1': float(np.std(index1_numeric))
        }
        
        print(f"INDEX1 - Sync: {self.statistiques['index1']['proportions'].get(0, 0):.3f}, Desync: {self.statistiques['index1']['proportions'].get(1, 0):.3f}")
        print(f"INDEX2 - A: {self.statistiques['index2']['proportions'].get('A', 0):.3f}, B: {self.statistiques['index2']['proportions'].get('B', 0):.3f}, C: {self.statistiques['index2']['proportions'].get('C', 0):.3f}")
        print(f"INDEX3 - BANKER: {self.statistiques['index3']['proportions'].get('BANKER', 0):.3f}, PLAYER: {self.statistiques['index3']['proportions'].get('PLAYER', 0):.3f}, TIE: {self.statistiques['index3']['proportions'].get('TIE', 0):.3f}")
        print(f"Entropie INDEX3: {self.statistiques['index3']['entropie_shannon']:.4f} bits")
        
    def calculer_matrices_transition(self):
        """
        Calcule les matrices de transition pour chaînes de Markov
        """
        print("\n🔄 CALCUL MATRICES DE TRANSITION")
        print("=" * 50)
        
        df = pd.DataFrame(self.mains)
        
        # 1. MATRICE TRANSITION INDEX3 (BANKER/PLAYER/TIE)
        transitions_index3 = defaultdict(lambda: defaultdict(int))
        for i in range(len(df) - 1):
            etat_actuel = df.iloc[i]['index3']
            etat_suivant = df.iloc[i + 1]['index3']
            transitions_index3[etat_actuel][etat_suivant] += 1
        
        # Normalisation en probabilités
        matrice_index3 = {}
        for etat_actuel in transitions_index3:
            total = sum(transitions_index3[etat_actuel].values())
            matrice_index3[etat_actuel] = {
                etat_suivant: count / total 
                for etat_suivant, count in transitions_index3[etat_actuel].items()
            }
        
        self.matrices_transition['index3'] = matrice_index3
        
        # 2. MATRICE TRANSITION INDEX5 (avec contraintes BCT)
        transitions_index5 = defaultdict(lambda: defaultdict(int))
        transitions_valides_bct = 0
        transitions_totales = 0
        
        for i in range(len(df) - 1):
            etat_actuel = df.iloc[i]['index5']
            etat_suivant = df.iloc[i + 1]['index5']
            transitions_index5[etat_actuel][etat_suivant] += 1
            transitions_totales += 1
            
            # Vérification contraintes BCT
            if self._verifier_transition_bct(etat_actuel, etat_suivant):
                transitions_valides_bct += 1
        
        # Normalisation
        matrice_index5 = {}
        for etat_actuel in transitions_index5:
            total = sum(transitions_index5[etat_actuel].values())
            matrice_index5[etat_actuel] = {
                etat_suivant: count / total 
                for etat_suivant, count in transitions_index5[etat_actuel].items()
            }
        
        self.matrices_transition['index5'] = matrice_index5
        self.matrices_transition['conformite_bct'] = transitions_valides_bct / transitions_totales
        
        print(f"Matrice INDEX3: {len(matrice_index3)} états")
        print(f"Matrice INDEX5: {len(matrice_index5)} états")
        print(f"Conformité BCT: {self.matrices_transition['conformite_bct']:.3f}")
        
    def _verifier_transition_bct(self, etat_actuel: str, etat_suivant: str) -> bool:
        """
        Vérifie si une transition respecte les règles BCT
        """
        try:
            parts_actuel = etat_actuel.split('_')
            parts_suivant = etat_suivant.split('_')
            
            if len(parts_actuel) != 3 or len(parts_suivant) != 3:
                return False
            
            index1_actuel = int(parts_actuel[0])
            index2_actuel = parts_actuel[1]
            index1_suivant = int(parts_suivant[0])
            
            # Règles BCT
            if index2_actuel == 'C':
                # C → Alternance
                return index1_suivant != index1_actuel
            else:  # A ou B
                # A,B → Conservation
                return index1_suivant == index1_actuel
                
        except:
            return False
    
    def _calculer_entropie_shannon(self, counts) -> float:
        """Calcule l'entropie de Shannon"""
        total = sum(counts)
        if total == 0:
            return 0.0
        
        entropie = 0.0
        for count in counts:
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)
        
        return entropie
    
    def _test_uniformite(self, counts) -> Dict:
        """
        Test du chi-carré pour l'uniformité
        """
        try:
            # Test chi-carré d'uniformité
            expected = np.mean(counts)
            chi2_stat = np.sum((np.array(counts) - expected)**2 / expected)
            dof = len(counts) - 1
            p_value = 1 - stats.chi2.cdf(chi2_stat, dof)

            return {
                'chi2_statistic': float(chi2_stat),
                'p_value': float(p_value),
                'degrees_freedom': dof,
                'uniforme': p_value > 0.05
            }
        except:
            return {'chi2_statistic': 0, 'p_value': 1, 'degrees_freedom': 0, 'uniforme': True}

    def calculer_entropies_avancees(self):
        """
        Calcule les entropies selon la théorie de l'information
        """
        print("\n🔢 CALCUL ENTROPIES AVANCÉES")
        print("=" * 50)

        df = pd.DataFrame(self.mains)

        # 1. ENTROPIE DE CHAÎNE DE MARKOV INDEX3
        entropie_markov_index3 = self._calculer_entropie_markov(
            self.matrices_transition['index3'],
            self.statistiques['index3']['proportions']
        )

        # 2. INFORMATION MUTUELLE INDEX1-INDEX3
        info_mutuelle_1_3 = self._calculer_information_mutuelle(
            df, 'index1', 'index3'
        )

        # 3. ENTROPIE CONDITIONNELLE H(INDEX3|INDEX1)
        entropie_conditionnelle = self._calculer_entropie_conditionnelle(
            df, 'index3', 'index1'
        )

        # 4. ENTROPIE JOINTE H(INDEX1,INDEX2,INDEX3)
        entropie_jointe = self._calculer_entropie_jointe(
            df, ['index1', 'index2', 'index3']
        )

        self.entropies = {
            'markov_index3': entropie_markov_index3,
            'information_mutuelle_1_3': info_mutuelle_1_3,
            'conditionnelle_3_given_1': entropie_conditionnelle,
            'jointe_1_2_3': entropie_jointe,
            'taux_entropie': entropie_markov_index3  # Pour chaîne de Markov
        }

        print(f"Entropie Markov INDEX3: {entropie_markov_index3:.4f} bits")
        print(f"Information mutuelle I(INDEX1;INDEX3): {info_mutuelle_1_3:.4f} bits")
        print(f"Entropie conditionnelle H(INDEX3|INDEX1): {entropie_conditionnelle:.4f} bits")

    def _calculer_entropie_markov(self, matrice_transition: Dict, distribution_stationnaire: Dict) -> float:
        """
        Calcule l'entropie de chaîne de Markov: H(Ξ) = -∑∑ μ(x) P(y|x) log₂(P(y|x))
        """
        entropie = 0.0

        for etat_x in matrice_transition:
            mu_x = distribution_stationnaire.get(etat_x, 0)
            if mu_x > 0:
                for etat_y in matrice_transition[etat_x]:
                    p_xy = matrice_transition[etat_x][etat_y]
                    if p_xy > 0:
                        entropie -= mu_x * p_xy * math.log2(p_xy)

        return entropie

    def _calculer_information_mutuelle(self, df: pd.DataFrame, col1: str, col2: str) -> float:
        """
        Calcule l'information mutuelle I(X;Y) = ∑∑ p(x,y) log₂(p(x,y)/(p(x)p(y)))
        """
        # Table de contingence
        contingency = pd.crosstab(df[col1], df[col2])
        total = contingency.sum().sum()

        # Probabilités jointes et marginales
        p_joint = contingency / total
        p_x = contingency.sum(axis=1) / total
        p_y = contingency.sum(axis=0) / total

        info_mutuelle = 0.0
        for x in p_joint.index:
            for y in p_joint.columns:
                if p_joint.loc[x, y] > 0:
                    info_mutuelle += p_joint.loc[x, y] * math.log2(
                        p_joint.loc[x, y] / (p_x[x] * p_y[y])
                    )

        return info_mutuelle

    def _calculer_entropie_conditionnelle(self, df: pd.DataFrame, col_y: str, col_x: str) -> float:
        """
        Calcule l'entropie conditionnelle H(Y|X) = -∑∑ p(x,y) log₂(p(y|x))
        """
        contingency = pd.crosstab(df[col_x], df[col_y])
        total = contingency.sum().sum()

        entropie_cond = 0.0
        for x in contingency.index:
            p_x = contingency.loc[x].sum() / total
            if p_x > 0:
                for y in contingency.columns:
                    p_xy = contingency.loc[x, y] / total
                    if p_xy > 0:
                        p_y_given_x = contingency.loc[x, y] / contingency.loc[x].sum()
                        entropie_cond -= p_xy * math.log2(p_y_given_x)

        return entropie_cond

    def _calculer_entropie_jointe(self, df: pd.DataFrame, colonnes: List[str]) -> float:
        """
        Calcule l'entropie jointe H(X1,X2,...,Xn)
        """
        # Création des tuples pour chaque combinaison
        combinaisons = df[colonnes].apply(tuple, axis=1)
        counts = combinaisons.value_counts()

        return self._calculer_entropie_shannon(counts.values)

    def generer_visualisations_professionnelles(self):
        """
        Génère toutes les visualisations professionnelles
        """
        print("\n📈 GÉNÉRATION VISUALISATIONS PROFESSIONNELLES")
        print("=" * 50)

        # Configuration pour graphiques de qualité
        plt.style.use('seaborn-v0_8')
        fig = plt.figure(figsize=(20, 16))

        # 1. DISTRIBUTION INDEX3 (Graphique principal)
        ax1 = plt.subplot(3, 3, 1)
        index3_data = self.statistiques['index3']['distribution']
        colors = ['#2E86AB', '#A23B72', '#F18F01']
        bars = ax1.bar(index3_data.keys(), index3_data.values(), color=colors, alpha=0.8)
        ax1.set_title('Distribution INDEX3 (BANKER/PLAYER/TIE)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Nombre de mains')

        # Ajout des pourcentages sur les barres
        for bar, (key, value) in zip(bars, index3_data.items()):
            percentage = self.statistiques['index3']['proportions'][key] * 100
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,
                    f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')

        # 2. MATRICE DE TRANSITION INDEX3 (Heatmap)
        ax2 = plt.subplot(3, 3, 2)
        matrice_df = pd.DataFrame(self.matrices_transition['index3']).fillna(0)
        sns.heatmap(matrice_df, annot=True, fmt='.3f', cmap='Blues', ax=ax2)
        ax2.set_title('Matrice Transition INDEX3', fontsize=14, fontweight='bold')
        ax2.set_xlabel('État suivant')
        ax2.set_ylabel('État actuel')

        # 3. DISTRIBUTION INDEX2 (A/B/C)
        ax3 = plt.subplot(3, 3, 3)
        index2_data = self.statistiques['index2']['distribution']
        colors_index2 = ['#FF6B6B', '#4ECDC4', '#45B7D1']
        wedges, texts, autotexts = ax3.pie(index2_data.values(), labels=index2_data.keys(),
                                          autopct='%1.1f%%', colors=colors_index2, startangle=90)
        ax3.set_title('Distribution INDEX2 (A/B/C)', fontsize=14, fontweight='bold')

        # 4. ENTROPIES COMPARÉES
        ax4 = plt.subplot(3, 3, 4)
        entropies_data = {
            'Shannon INDEX3': self.statistiques['index3']['entropie_shannon'],
            'Markov INDEX3': self.entropies['markov_index3'],
            'Conditionnelle': self.entropies['conditionnelle_3_given_1'],
            'Info Mutuelle': self.entropies['information_mutuelle_1_3']
        }
        bars = ax4.bar(range(len(entropies_data)), list(entropies_data.values()),
                      color=['#FF9999', '#66B2FF', '#99FF99', '#FFCC99'])
        ax4.set_xticks(range(len(entropies_data)))
        ax4.set_xticklabels(entropies_data.keys(), rotation=45, ha='right')
        ax4.set_title('Entropies Informationnelles', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Bits')

        # Valeurs sur les barres
        for bar, value in zip(bars, entropies_data.values()):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # 5. DISTRIBUTION INDEX1 (SYNC/DESYNC)
        ax5 = plt.subplot(3, 3, 5)
        index1_data = self.statistiques['index1']['distribution']
        labels = ['SYNC (0)', 'DESYNC (1)']
        colors_index1 = ['#90EE90', '#FFB6C1']
        bars = ax5.bar(labels, index1_data.values(), color=colors_index1, alpha=0.8)
        ax5.set_title('Distribution INDEX1 (SYNC/DESYNC)', fontsize=14, fontweight='bold')
        ax5.set_ylabel('Nombre de mains')

        # Pourcentages
        for bar, (key, value) in zip(bars, index1_data.items()):
            percentage = self.statistiques['index1']['proportions'][key] * 100
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.01,
                    f'{percentage:.1f}%', ha='center', va='bottom', fontweight='bold')

        # 6. TOP 10 INDEX5 (Valeurs les plus fréquentes)
        ax6 = plt.subplot(3, 3, 6)
        index5_data = self.statistiques['index5']['distribution']
        top10_index5 = dict(sorted(index5_data.items(), key=lambda x: x[1], reverse=True)[:10])
        bars = ax6.bar(range(len(top10_index5)), list(top10_index5.values()),
                      color='lightcoral', alpha=0.8)
        ax6.set_xticks(range(len(top10_index5)))
        ax6.set_xticklabels(list(top10_index5.keys()), rotation=45, ha='right')
        ax6.set_title('TOP 10 INDEX5 (Plus fréquents)', fontsize=14, fontweight='bold')
        ax6.set_ylabel('Fréquence')

        # 7. CONFORMITÉ BCT
        ax7 = plt.subplot(3, 3, 7)
        conformite = self.matrices_transition['conformite_bct']
        non_conformite = 1 - conformite
        labels = ['Conforme BCT', 'Non-conforme']
        sizes = [conformite, non_conformite]
        colors = ['#90EE90', '#FFB6C1']
        wedges, texts, autotexts = ax7.pie(sizes, labels=labels, autopct='%1.1f%%',
                                          colors=colors, startangle=90)
        ax7.set_title('Conformité Règles BCT', fontsize=14, fontweight='bold')

        # 8. MOMENTS STATISTIQUES INDEX1
        ax8 = plt.subplot(3, 3, 8)
        moments = self.statistiques['moments']
        moments_data = {
            'Moyenne': moments['moyenne_index1'],
            'Variance': moments['variance_index1'],
            'Skewness': moments['skewness_index1'],
            'Kurtosis': moments['kurtosis_index1']
        }
        bars = ax8.bar(range(len(moments_data)), list(moments_data.values()),
                      color=['#FFD700', '#FF6347', '#98FB98', '#DDA0DD'])
        ax8.set_xticks(range(len(moments_data)))
        ax8.set_xticklabels(moments_data.keys(), rotation=45, ha='right')
        ax8.set_title('Moments Statistiques INDEX1', fontsize=14, fontweight='bold')

        # Valeurs sur les barres
        for bar, value in zip(bars, moments_data.values()):
            ax8.text(bar.get_x() + bar.get_width()/2, bar.get_height() + abs(value)*0.05,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        # 9. TESTS D'UNIFORMITÉ (p-values)
        ax9 = plt.subplot(3, 3, 9)
        tests_data = {
            'INDEX1': self.statistiques['index1']['test_uniformite']['p_value'],
            'INDEX2': self.statistiques['index2']['test_uniformite']['p_value'],
            'INDEX3': self.statistiques['index3']['test_uniformite']['p_value']
        }
        colors_tests = ['red' if p < 0.05 else 'green' for p in tests_data.values()]
        bars = ax9.bar(tests_data.keys(), list(tests_data.values()), color=colors_tests, alpha=0.7)
        ax9.axhline(y=0.05, color='red', linestyle='--', alpha=0.7, label='Seuil α=0.05')
        ax9.set_title('Tests Uniformité (p-values)', fontsize=14, fontweight='bold')
        ax9.set_ylabel('p-value')
        ax9.legend()

        # Valeurs sur les barres
        for bar, value in zip(bars, tests_data.values()):
            ax9.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig('analyse_complete_baccarat.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✅ Visualisations sauvegardées: analyse_complete_baccarat.png")

    def generer_rapport_complet(self):
        """
        Génère un rapport complet au format texte
        """
        print("\n📄 GÉNÉRATION RAPPORT COMPLET")
        print("=" * 50)

        rapport = f"""
# RAPPORT D'ANALYSE STATISTIQUE PROFESSIONNELLE BACCARAT
================================================================

## RÉSUMÉ EXÉCUTIF
- **Total mains analysées**: {len(self.mains):,}
- **Entropie INDEX3**: {self.statistiques['index3']['entropie_shannon']:.4f} bits
- **Information mutuelle I(INDEX1;INDEX3)**: {self.entropies['information_mutuelle_1_3']:.4f} bits
- **Conformité BCT**: {self.matrices_transition['conformite_bct']:.1%}

## DISTRIBUTIONS PRINCIPALES

### INDEX3 (OBJECTIF PRÉDICTION)
- **BANKER**: {self.statistiques['index3']['proportions'].get('BANKER', 0):.1%} ({self.statistiques['index3']['distribution'].get('BANKER', 0):,} mains)
- **PLAYER**: {self.statistiques['index3']['proportions'].get('PLAYER', 0):.1%} ({self.statistiques['index3']['distribution'].get('PLAYER', 0):,} mains)
- **TIE**: {self.statistiques['index3']['proportions'].get('TIE', 0):.1%} ({self.statistiques['index3']['distribution'].get('TIE', 0):,} mains)

### INDEX1 (SYNC/DESYNC)
- **SYNC (0)**: {self.statistiques['index1']['proportions'].get(0, 0):.1%}
- **DESYNC (1)**: {self.statistiques['index1']['proportions'].get(1, 0):.1%}

### INDEX2 (A/B/C)
- **A**: {self.statistiques['index2']['proportions'].get('A', 0):.1%}
- **B**: {self.statistiques['index2']['proportions'].get('B', 0):.1%}
- **C**: {self.statistiques['index2']['proportions'].get('C', 0):.1%}

## ANALYSE INFORMATIONNELLE

### Entropies (en bits)
- **Shannon INDEX3**: {self.statistiques['index3']['entropie_shannon']:.4f}
- **Markov INDEX3**: {self.entropies['markov_index3']:.4f}
- **Conditionnelle H(INDEX3|INDEX1)**: {self.entropies['conditionnelle_3_given_1']:.4f}
- **Jointe H(INDEX1,INDEX2,INDEX3)**: {self.entropies['jointe_1_2_3']:.4f}

### Information Mutuelle
- **I(INDEX1;INDEX3)**: {self.entropies['information_mutuelle_1_3']:.4f} bits
- **Réduction incertitude**: {(self.entropies['information_mutuelle_1_3']/self.statistiques['index3']['entropie_shannon']*100):.1f}%

## TESTS STATISTIQUES

### Tests d'Uniformité (Chi-carré)
- **INDEX1**: p-value = {self.statistiques['index1']['test_uniformite']['p_value']:.4f} ({'Uniforme' if self.statistiques['index1']['test_uniformite']['uniforme'] else 'Non-uniforme'})
- **INDEX2**: p-value = {self.statistiques['index2']['test_uniformite']['p_value']:.4f} ({'Uniforme' if self.statistiques['index2']['test_uniformite']['uniforme'] else 'Non-uniforme'})
- **INDEX3**: p-value = {self.statistiques['index3']['test_uniformite']['p_value']:.4f} ({'Uniforme' if self.statistiques['index3']['test_uniformite']['uniforme'] else 'Non-uniforme'})

## MOMENTS STATISTIQUES INDEX1
- **Moyenne**: {self.statistiques['moments']['moyenne_index1']:.4f}
- **Variance**: {self.statistiques['moments']['variance_index1']:.4f}
- **Skewness**: {self.statistiques['moments']['skewness_index1']:.4f}
- **Kurtosis**: {self.statistiques['moments']['kurtosis_index1']:.4f}

## CONFORMITÉ RÈGLES BCT
- **Transitions conformes**: {self.matrices_transition['conformite_bct']:.1%}
- **Règle C → Alternance**: Vérifiée
- **Règle A,B → Conservation**: Vérifiée

## RECOMMANDATIONS POUR PRÉDICTION
1. **Baseline BANKER**: {self.statistiques['index3']['baseline_banker']:.1%} (référence à battre)
2. **Information INDEX1**: Utiliser I(INDEX1;INDEX3) = {self.entropies['information_mutuelle_1_3']:.4f} bits
3. **Chaîne de Markov**: Entropie = {self.entropies['markov_index3']:.4f} bits
4. **Objectif précision**: > 70% (vs {self.statistiques['index3']['baseline_banker']:.1%} baseline)

================================================================
Rapport généré par Analyseur Professionnel Baccarat v1.0
Date: 2025-06-30
================================================================
"""

        with open('rapport_analyse_complete.txt', 'w', encoding='utf-8') as f:
            f.write(rapport)

        print("✅ Rapport sauvegardé: rapport_analyse_complete.txt")
        return rapport

if __name__ == "__main__":
    print("🚀 ANALYSEUR PROFESSIONNEL BACCARAT")
    print("=" * 50)

    # Test avec partie.txt
    analyseur = AnalyseurProfessionnelBaccarat()

    if analyseur.charger_donnees('dataset_baccarat_lupasco_20250629_165801.json'):
        analyseur.calculer_statistiques_descriptives()
        analyseur.calculer_matrices_transition()
        analyseur.calculer_entropies_avancees()
        analyseur.generer_visualisations_professionnelles()
        analyseur.generer_rapport_complet()
        print("\n🎯 ANALYSE COMPLÈTE TERMINÉE!")
    else:
        print("❌ Impossible de charger les données")

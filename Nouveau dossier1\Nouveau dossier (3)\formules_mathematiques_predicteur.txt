FORMULES MATHÉMATIQUES DU PRÉDICTEUR FRACTAL INDEX5
=====================================================

Ce document rassemble toutes les formules mathématiques identifiées dans le programme predicteur_fractal_index5.py

1. LISSAGE DE LAPLACE (Probabilités conditionnelles)
===================================================
Formule utilisée dans le programme:
P(INDEX3|INDEX1,INDEX2) = (count + α) / (total + α*|INDEX3|)

Où:
- count = nombre d'occurrences de INDEX3 pour la combinaison (INDEX1,INDEX2)
- α = paramètre de lissage (ALPHA_LAPLACE = 1.0)
- total = nombre total d'observations pour (INDEX1,INDEX2)
- |INDEX3| = nombre de valeurs possibles pour INDEX3 (3: PLAYER, BANKER, TIE)

2. INTERVALLES DE CONFIANCE (Approximation normale)
==================================================
Formules utilisées:
- Erreur standard: se = √(p * (1 - p) / n)
- Marge d'erreur: marge = z * se
- Intervalle: [p - marge, p + marge]

Où:
- p = proportion estimée
- n = taille de l'échantillon
- z = 1.96 (pour 95% de confiance)

3. EXPOSANT DE HURST (Analyse fractale)
======================================
Méthode DFA (Detrended Fluctuation Analysis):
- Segmentation en échelles logarithmiques
- Calcul des fluctuations: F(s) = √(1/N * Σ[y(i) - yn(i)]²)
- Régression log-log: log(F(s)) = H * log(s) + constante
- H = exposant de Hurst

4. ENTROPIE DE SHANNON
=====================
Formule classique:
H(X) = -Σ p(xi) * log₂(p(xi))

Normalisation pour 3 catégories:
H_norm = H(X) / log₂(3)

5. ENTROPIE APPROXIMATIVE (ApEn)
===============================
ApEn(m,r,N) = φ(m) - φ(m+1)

Où:
φ(m) = (1/N-m+1) * Σ log(Cᵢᵐ(r)/N-m+1)

6. WILSON SCORE INTERVALS
========================
Pour proportions multinomiales:
Confiance = 1 - (largeur_IC / 2.0)

7. ESTIMATION BAYÉSIENNE DIRICHLET
=================================
Avec prior Dirichlet(α₁, α₂, α₃):
P(θᵢ|data) = (countᵢ + αᵢ) / (total + Σαⱼ)

8. INFORMATION MUTUELLE
======================
I(X;Y) = Σ p(x,y) * log(p(x,y) / (p(x)*p(y)))

9. DÉVIATIONS FRACTALES
======================
deviation = proportion_observée - proportion_référence
force_rappel = -deviation * 2.0

10. RÉGRESSION POLYNOMIALE
=========================
Utilisée dans l'analyse de Hurst:
trend = np.polyfit(range(len(segment)), segment, 1)
detrended = segment - np.polyval(trend, range(len(segment)))

11. FLUCTUATIONS RMS
===================
RMS = √(1/N * Σ(xᵢ - x̄)²)

12. FUSION ADAPTATIVE DE CONFIANCES
==================================
confiance_finale = Σ(wᵢ * confiance_ᵢ)

Avec normalisation des poids:
wᵢ = poids_ᵢ / Σ(poids_ⱼ)

13. CALCULS DE PERFORMANCE
=========================
Taux de réussite = predictions_correctes / predictions_valides
Ratio = count / total

14. VARIANCE SIMULÉE
===================
variance = p * (1 - p) / n

15. FACTEUR D'OUBLI TEMPOREL
===========================
Adaptation temporelle avec facteur 0.98:
poids_temporel = facteur_oubli^(age_observation)

RÉFÉRENCES PDF ACADÉMIQUES
==========================

1. LISSAGE DE LAPLACE
URL: https://web.stanford.edu/~jurafsky/slp3/3.pdf
Titre: "N-gram Language Models" - Stanford University
Description: Chapitre sur le lissage de Laplace pour l'estimation de probabilités

URL: https://arxiv.org/pdf/1709.08314
Titre: "Confidence Interval of Probability Estimator of Laplace Smoothing"
Description: Intervalles de confiance pour l'estimateur de Laplace

2. INTERVALLES DE CONFIANCE WILSON SCORE
URL: https://www.lexjansen.com/wuss/2016/127_Final_Paper_PDF.pdf
Titre: "Constructing Confidence Intervals for the Differences of Binomial Proportions"
Description: Méthodes Wilson Score pour proportions binomiales

3. EXPOSANT DE HURST / DFA
URL: https://citeseerx.ist.psu.edu/document?repid=rep1&type=pdf&doi=4acac7911462ca827cb11283f52b0c24ebdafbe8
Titre: "Fourier detrended fluctuation analysis"
Description: Analyse de fluctuation détendancée pour l'exposant de Hurst

URL: https://pmc.ncbi.nlm.nih.gov/articles/PMC3366552/
Titre: "Introduction to Multifractal Detrended Fluctuation Analysis in Matlab"
Description: Guide complet sur l'analyse DFA multifractale

4. ENTROPIE DE SHANNON
URL: https://people.math.harvard.edu/~ctm/home/<USER>/others/shannon/entropy/entropy.pdf
Titre: "A Mathematical Theory of Communication" - Claude Shannon (1948)
Description: Article fondateur de la théorie de l'information

URL: https://ee.stanford.edu/~gray/it.pdf
Titre: "Entropy and Information Theory" - Stanford University
Description: Cours complet sur l'entropie et la théorie de l'information

5. ENTROPIE APPROXIMATIVE (ApEn)
URL: https://www.pnas.org/doi/pdf/10.1073/pnas.88.6.2297
Titre: "Approximate entropy as a measure of system complexity" - Pincus (1991)
Description: Article original définissant l'entropie approximative

URL: https://biosignal.uconn.edu/wp-content/uploads/sites/2503/2018/09/06_Lu_2008_TBME.pdf
Titre: "Automatic Selection of the Threshold Value r for Approximate Entropy"
Description: Sélection automatique des paramètres pour ApEn

6. DISTRIBUTION DIRICHLET / ESTIMATION BAYÉSIENNE
URL: https://www.jmlr.org/papers/volume3/blei03a/blei03a.pdf
Titre: "Latent Dirichlet Allocation" - Blei, Ng, Jordan (2003)
Description: Allocation Dirichlet latente et estimation bayésienne

URL: https://users.cecs.anu.edu.au/~ssanner/MLSS2010/Johnson1.pdf
Titre: "Bayesian Inference for Dirichlet-Multinomials"
Description: Inférence bayésienne pour distributions Dirichlet-multinomiales

URL: https://apps-afsc.fisheries.noaa.gov/Plan_Team/2022_pop_cie/Thorson%20et%20al%202017%20Dirichlet%20multinomial.pdf
Titre: "Dirichlet-multinomial distribution for compositional data"
Description: Application de la distribution Dirichlet-multinomiale

7. INFORMATION MUTUELLE
URL: https://people.cs.umass.edu/~elm/Teaching/Docs/mutInf.pdf
Titre: "Entropy and Mutual Information" - University of Massachusetts
Description: Cours sur l'entropie et l'information mutuelle

URL: http://www.ece.tufts.edu/ee/194NIT/lect01.pdf
Titre: "Lecture 1: Entropy and mutual information" - Tufts University
Description: Introduction à l'entropie et l'information mutuelle

8. CLASSIFICATION NAÏVE BAYES AVEC LISSAGE LAPLACIEN
URL: https://pubs.aip.org/aip/acp/article-pdf/doi/10.1063/5.0116519/16222811/030004_1_online.pdf
Titre: "Classification Data Mining with Laplacian Smoothing on Naïve Bayes method"
Description: Application du lissage laplacien aux classificateurs Naïve Bayes

9. RÉGRESSION POLYNOMIALE / MOINDRES CARRÉS
URL: https://people.duke.edu/~hpgavin/lm.pdf
Titre: "The Levenberg-Marquardt algorithm for nonlinear least squares curve-fitting"
Description: Algorithmes de moindres carrés pour l'ajustement de courbes

URL: https://www.geometrictools.com/Documentation/LeastSquaresFitting.pdf
Titre: "Least Squares Fitting of Data by Linear or Quadratic Structures"
Description: Ajustement par moindres carrés pour structures linéaires et quadratiques

URL: http://web.cecs.pdx.edu/gerry/nmm/course/slides/ch09Slides.pdf
Titre: "Least Squares Fitting of Data to a Curve"
Description: Cours sur l'ajustement de courbes par moindres carrés

10. RMS (ROOT MEAN SQUARE)
URL: https://pcmdi.llnl.gov/staff/taylor/CV/Taylor_diagram_primer.pdf
Titre: "Taylor Diagram Primer" - Karl E. Taylor
Description: Diagrammes de Taylor et statistiques RMS centrées

URL: https://gmd.copernicus.org/articles/7/1247/2014/gmd-7-1247-2014.pdf
Titre: "Root mean square error (RMSE) or mean absolute error (MAE)"
Description: Comparaison des métriques d'erreur RMS et MAE

FORMULES MATHÉMATIQUES COMPLÈTES EXTRAITES DU CODE
=================================================

1. LISSAGE DE LAPLACE (Ligne 310-312)
P_PLAYER = (compteurs['PLAYER'] + alpha) / total_lisse
P_BANKER = (compteurs['BANKER'] + alpha) / total_lisse
P_TIE = (compteurs['TIE'] + alpha) / total_lisse
Où: total_lisse = compteurs['total'] + alpha * 3

2. INTERVALLES DE CONFIANCE (Ligne 342-346)
se = (p * (1 - p) / n) ** 0.5
marge = z * se
intervalles[f'P_{outcome}'] = (max(0, p - marge), min(1, p + marge))

3. EXPOSANT DE HURST - ÉCHELLES LOGARITHMIQUES (Ligne 372)
scales = np.logspace(0.5, np.log10(N//4), 10).astype(int)

4. EXPOSANT DE HURST - RÉGRESSION LOG-LOG (Ligne 393-396)
log_scales = np.log10(scales[:len(fluctuations)])
log_flucs = np.log10(fluctuations)
hurst = np.polyfit(log_scales, log_flucs, 1)[0]
return max(0.1, min(0.9, hurst))

5. DÉVIATIONS FRACTALES (Ligne 425-428)
deviation = proportions_observees[categorie] - self.proportions_fractales[categorie]
force_rappel = -deviation * 2.0
deviations[categorie] = force_rappel

6. FLUCTUATIONS DFA (Ligne 384-388)
segment = ts[i*scale:(i+1)*scale]
trend = np.polyfit(range(len(segment)), segment, 1)
detrended = segment - np.polyval(trend, range(len(segment)))
fluc += np.sqrt(np.mean(detrended**2))

7. CONFIANCE WILSON (Ligne 670)
confiance_wilson = max(0.1, 1.0 - (largeur_ic / 2.0))

8. ENTROPIE DE SHANNON (Ligne 1228)
entropie = -sum((count/total) * np.log2(count/total) for count in compteur.values())
return entropie / np.log2(3)  # Normalisation pour 3 catégories

9. ENTROPIE APPROXIMATIVE - PHI (Ligne 1219)
phi = np.mean(np.log(C / len(patterns)))

10. VARIANCE SIMULÉE (Ligne 622)
variance_simulee = prob_max * (1 - prob_max) / max(30, len(self.historique))

11. FUSION ADAPTATIVE (Ligne 964-967)
confiance_base = (conf_prob * poids_prob + conf_fractal * poids_fractal)
confiance_finale = 0.8 * confiance_base + 0.2 * conf_fusion

12. BONUS FRACTAL HURST (Ligne 993-997)
if hurst_moyen > 0.7:
    bonus = 0.05 * (hurst_moyen - 0.7)
elif hurst_moyen < 0.3:
    bonus = 0.03 * (0.3 - hurst_moyen)

13. TAUX DE RÉUSSITE (Ligne 1158-1159)
taux_predictions_valides = (self.stats_predictions['predictions_correctes'] /
                          self.stats_predictions['predictions_valides'])

14. VOTE PONDÉRÉ (Ligne 1352-1353)
for pred, poids_pred in zip(predictions, poids):
    votes[pred] += poids_pred

15. NORMALISATION DES POIDS (Ligne 1347-1348)
poids_total = sum(poids)
poids = [p / poids_total for p in poids]

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRÉDICTEUR MATHÉMATIQUE EXPERT
==============================

Basé sur la recherche exhaustive des fichiers .tex et .md issus de :
- abramowitz_and_stegun.pdf  
- Elements_of_Information_Theory_Elements.pdf

Utilise les concepts mathématiques avancés :
- Fractales et auto-similarité (patterns INDEX5)
- Entropie de Shannon et théorie de l'information
- Chaînes de Markov avec analyse spectrale  
- Estimation du maximum de vraisemblance
- Méthode d'entropie maximale de Burg
- Information de Fisher et bornes de Cramér-Rao

CONTRAINTES RESPECTÉES :
- Pas d'entraînement global
- Analyse uniquement de la partie en cours
- Chaque partie est indépendante
"""

import json
import math
import numpy as np
from typing import List, Dict, Any, Tuple
from collections import defaultdict, Counter
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PredicteurMathematiqueExpert:
    """
    Prédicteur basé sur les mathématiques avancées extraites des fichiers de référence
    """
    
    def __init__(self):
        """Initialisation du prédicteur expert"""
        logger.info("🎯 Initialisation du Prédicteur Mathématique Expert")
        logger.info("📚 Basé sur Abramowitz & Stegun + Elements of Information Theory")
        
        # Constantes mathématiques issues de la recherche
        self.ENTROPIE_MAX_3_OUTCOMES = math.log2(3)  # Pour BANKER, PLAYER, TIE
        self.SEUIL_FISHER_INFORMATION = 0.1
        self.FACTEUR_CRAMER_RAO = 1.96  # 95% confidence interval
        
        # Patterns fractals identifiés dans rapport_proportions_index5
        self.RATIO_FRACTAL_SYNC_DESYNC = 0.497  # 49.7% observé
        
        # Transitions INDEX2 selon Base_index.txt
        self.TRANSITIONS_INDEX2 = {
            'A': 'A',  # A maintient
            'B': 'B',  # B maintient  
            'C': 'A'   # C flip vers A
        }
        
    def predire_index3_partie_courante(self, partie_courante: List[Dict]) -> Dict[str, Any]:
        """
        Prédiction INDEX3 pour la main n+1 basée UNIQUEMENT sur la partie courante
        
        Args:
            partie_courante: Liste des mains de la partie en cours (format partie.txt)
            
        Returns:
            Dict contenant la prédiction et les métriques mathématiques
        """
        if not partie_courante or len(partie_courante) < 2:
            return self._prediction_par_defaut("Pas assez de données dans la partie")
            
        logger.info(f"🔬 Analyse mathématique de la partie courante: {len(partie_courante)} mains")
        
        # 1. ANALYSE FRACTALE des patterns INDEX5
        analyse_fractale = self._analyser_patterns_fractals_index5(partie_courante)
        
        # 2. ENTROPIE DE SHANNON pour quantifier l'incertitude
        entropie_shannon = self._calculer_entropie_shannon_partie(partie_courante)
        
        # 3. CHAÎNE DE MARKOV avec analyse spectrale
        analyse_markov = self._analyser_chaine_markov_partie(partie_courante)
        
        # 4. ESTIMATION MAXIMUM DE VRAISEMBLANCE
        estimation_ml = self._estimation_maximum_vraisemblance(partie_courante)
        
        # 5. MÉTHODE ENTROPIE MAXIMALE DE BURG
        analyse_burg = self._methode_entropie_maximale_burg(partie_courante)
        
        # 6. INFORMATION DE FISHER et bornes de Cramér-Rao
        fisher_info = self._calculer_information_fisher(partie_courante)
        
        # 7. FUSION OPTIMALE selon théorie de l'information
        prediction_finale = self._fusion_theorique_information(
            analyse_fractale, entropie_shannon, analyse_markov,
            estimation_ml, analyse_burg, fisher_info
        )
        
        return prediction_finale
        
    def _analyser_patterns_fractals_index5(self, partie: List[Dict]) -> Dict[str, Any]:
        """
        Analyse fractale des patterns INDEX5 selon auto-similarité observée
        """
        logger.info("🔬 Analyse fractale INDEX5...")
        
        # Extraction des INDEX5 de la partie
        index5_sequence = [main.get('index5', '') for main in partie if main.get('index5')]
        
        if len(index5_sequence) < 3:
            return {'fractal_dimension': 0.0, 'auto_similarite': 0.0, 'prediction_fractale': 'BANKER'}
            
        # Calcul de la dimension fractale par box-counting
        dimension_fractale = self._calculer_dimension_fractale(index5_sequence)
        
        # Mesure d'auto-similarité
        auto_similarite = self._mesurer_auto_similarite(index5_sequence)
        
        # Prédiction basée sur les patterns fractals
        prediction_fractale = self._predire_par_fractales(index5_sequence)
        
        return {
            'fractal_dimension': dimension_fractale,
            'auto_similarite': auto_similarite,
            'prediction_fractale': prediction_fractale,
            'nb_patterns': len(index5_sequence)
        }
        
    def _calculer_entropie_shannon_partie(self, partie: List[Dict]) -> Dict[str, float]:
        """
        Calcul de l'entropie de Shannon H(X) = -∑ p(x) log₂ p(x)
        """
        logger.info("🔬 Calcul entropie de Shannon...")
        
        # Entropie INDEX3 (BANKER, PLAYER, TIE)
        index3_sequence = [main.get('index3', '') for main in partie if main.get('index3')]
        entropie_index3 = self._entropie_shannon(index3_sequence)
        
        # Entropie INDEX2 (A, B, C)  
        index2_sequence = [main.get('index2', '') for main in partie if main.get('index2')]
        entropie_index2 = self._entropie_shannon(index2_sequence)
        
        # Entropie conditionnelle H(INDEX3|INDEX2)
        entropie_conditionnelle = self._entropie_conditionnelle(partie)
        
        return {
            'entropie_index3': entropie_index3,
            'entropie_index2': entropie_index2, 
            'entropie_conditionnelle': entropie_conditionnelle,
            'information_mutuelle': entropie_index3 + entropie_index2 - entropie_conditionnelle
        }
        
    def _analyser_chaine_markov_partie(self, partie: List[Dict]) -> Dict[str, Any]:
        """
        Analyse de chaîne de Markov avec calcul de la distribution stationnaire
        """
        logger.info("🔬 Analyse chaîne de Markov...")
        
        # Construction de la matrice de transition INDEX3
        transitions = defaultdict(lambda: defaultdict(int))
        index3_sequence = [main.get('index3', '') for main in partie if main.get('index3')]
        
        for i in range(len(index3_sequence) - 1):
            etat_actuel = index3_sequence[i]
            etat_suivant = index3_sequence[i + 1]
            transitions[etat_actuel][etat_suivant] += 1
            
        # Normalisation pour obtenir les probabilités
        matrice_transition = {}
        for etat in transitions:
            total = sum(transitions[etat].values())
            if total > 0:
                matrice_transition[etat] = {
                    next_etat: count / total 
                    for next_etat, count in transitions[etat].items()
                }
                
        # Calcul de la distribution stationnaire (si possible)
        distribution_stationnaire = self._calculer_distribution_stationnaire(matrice_transition)
        
        # Prédiction basée sur le dernier état
        dernier_index3 = index3_sequence[-1] if index3_sequence else 'BANKER'
        prediction_markov = self._predire_markov(dernier_index3, matrice_transition)
        
        return {
            'matrice_transition': matrice_transition,
            'distribution_stationnaire': distribution_stationnaire,
            'prediction_markov': prediction_markov,
            'nb_transitions': len(index3_sequence) - 1
        }
        
    def _estimation_maximum_vraisemblance(self, partie: List[Dict]) -> Dict[str, Any]:
        """
        Estimation du maximum de vraisemblance θ̂ = argmax L(θ|x)
        """
        logger.info("🔬 Estimation maximum de vraisemblance...")
        
        # Comptage des occurrences INDEX3
        index3_counts = Counter(main.get('index3', '') for main in partie if main.get('index3'))
        total = sum(index3_counts.values())
        
        if total == 0:
            return {'estimation_ml': {'BANKER': 0.459, 'PLAYER': 0.446, 'TIE': 0.095}}
            
        # Estimation ML des probabilités
        estimation_ml = {
            outcome: count / total 
            for outcome, count in index3_counts.items()
        }
        
        # Compléter avec les outcomes manquants
        for outcome in ['BANKER', 'PLAYER', 'TIE']:
            if outcome not in estimation_ml:
                estimation_ml[outcome] = 0.0
                
        # Log-vraisemblance
        log_vraisemblance = sum(
            count * math.log(prob + 1e-10) 
            for outcome, prob in estimation_ml.items()
            for count in [index3_counts.get(outcome, 0)]
            if count > 0
        )
        
        return {
            'estimation_ml': estimation_ml,
            'log_vraisemblance': log_vraisemblance,
            'nb_observations': total
        }

    def _methode_entropie_maximale_burg(self, partie: List[Dict]) -> Dict[str, Any]:
        """
        Méthode d'entropie maximale de Burg pour estimation spectrale
        """
        logger.info("🔬 Méthode entropie maximale de Burg...")

        # Conversion INDEX3 en séquence numérique
        index3_to_num = {'BANKER': 0, 'PLAYER': 1, 'TIE': 2}
        sequence_numerique = [
            index3_to_num.get(main.get('index3', ''), 0)
            for main in partie if main.get('index3')
        ]

        if len(sequence_numerique) < 3:
            return {'coefficients_ar': [], 'entropie_maximale': 0.0}

        # Calcul des coefficients autorégressifs par méthode de Burg
        coefficients_ar = self._burg_coefficients(sequence_numerique)

        # Calcul de l'entropie maximale
        entropie_maximale = self._calculer_entropie_maximale(coefficients_ar, sequence_numerique)

        # Prédiction basée sur le modèle AR
        prediction_burg = self._predire_autoregressive(sequence_numerique, coefficients_ar)

        return {
            'coefficients_ar': coefficients_ar,
            'entropie_maximale': entropie_maximale,
            'prediction_burg': prediction_burg,
            'ordre_modele': len(coefficients_ar)
        }

    def _calculer_information_fisher(self, partie: List[Dict]) -> Dict[str, float]:
        """
        Calcul de l'information de Fisher J(θ) = E[(∂/∂θ ln f(X;θ))²]
        """
        logger.info("🔬 Calcul information de Fisher...")

        # Estimation des paramètres
        index3_counts = Counter(main.get('index3', '') for main in partie if main.get('index3'))
        total = sum(index3_counts.values())

        if total < 2:
            return {'fisher_information': 0.0, 'cramer_rao_bound': float('inf')}

        # Calcul de l'information de Fisher pour distribution multinomiale
        fisher_info = 0.0
        for outcome in ['BANKER', 'PLAYER', 'TIE']:
            count = index3_counts.get(outcome, 0)
            if count > 0:
                p = count / total
                fisher_info += count / (p * (1 - p))

        # Borne de Cramér-Rao
        cramer_rao_bound = 1.0 / fisher_info if fisher_info > 0 else float('inf')

        return {
            'fisher_information': fisher_info,
            'cramer_rao_bound': cramer_rao_bound,
            'efficacite_estimation': min(1.0, 1.0 / (cramer_rao_bound * total)) if cramer_rao_bound < float('inf') else 0.0
        }

    def _fusion_theorique_information(self, analyse_fractale: Dict, entropie_shannon: Dict,
                                    analyse_markov: Dict, estimation_ml: Dict,
                                    analyse_burg: Dict, fisher_info: Dict) -> Dict[str, Any]:
        """
        Fusion optimale selon la théorie de l'information
        """
        logger.info("🔬 Fusion selon théorie de l'information...")

        # Calcul des poids basés sur l'information de Fisher et l'entropie
        poids_fractale = 1.0 / (1.0 + entropie_shannon['entropie_index3'])
        poids_markov = analyse_markov['nb_transitions'] / 100.0  # Normalisation
        poids_ml = fisher_info['efficacite_estimation']
        poids_burg = 1.0 / (1.0 + analyse_burg['entropie_maximale'])

        # Normalisation des poids
        total_poids = poids_fractale + poids_markov + poids_ml + poids_burg
        if total_poids > 0:
            poids_fractale /= total_poids
            poids_markov /= total_poids
            poids_ml /= total_poids
            poids_burg /= total_poids
        else:
            poids_fractale = poids_markov = poids_ml = poids_burg = 0.25

        # Fusion des prédictions
        predictions = {
            'fractale': analyse_fractale['prediction_fractale'],
            'markov': analyse_markov['prediction_markov'],
            'ml': max(estimation_ml['estimation_ml'].keys(),
                     key=lambda k: estimation_ml['estimation_ml'][k]),
            'burg': analyse_burg['prediction_burg']
        }

        # Vote pondéré
        votes = defaultdict(float)
        votes[predictions['fractale']] += poids_fractale
        votes[predictions['markov']] += poids_markov
        votes[predictions['ml']] += poids_ml
        votes[predictions['burg']] += poids_burg

        # Prédiction finale
        prediction_finale = max(votes.keys(), key=lambda k: votes[k]) if votes else 'BANKER'
        confiance = max(votes.values()) if votes else 0.5

        return {
            'prediction': prediction_finale,
            'confiance': confiance,
            'probabilites': dict(estimation_ml['estimation_ml']),
            'poids_utilises': {
                'fractale': poids_fractale,
                'markov': poids_markov,
                'ml': poids_ml,
                'burg': poids_burg
            },
            'predictions_individuelles': predictions,
            'entropie_partie': entropie_shannon['entropie_index3'],
            'information_fisher': fisher_info['fisher_information'],
            'methode': 'FUSION_THEORIQUE_INFORMATION'
        }

    # ========================================================================
    # MÉTHODES MATHÉMATIQUES DE SUPPORT
    # ========================================================================

    def _calculer_dimension_fractale(self, sequence: List[str]) -> float:
        """
        Calcul de la dimension fractale par box-counting
        """
        if len(sequence) < 4:
            return 0.0

        # Conversion en séquence numérique pour box-counting
        unique_values = list(set(sequence))
        value_to_num = {val: i for i, val in enumerate(unique_values)}
        numeric_sequence = [value_to_num[val] for val in sequence]

        # Box-counting sur différentes échelles
        scales = [2, 3, 4, 5]
        box_counts = []

        for scale in scales:
            boxes = set()
            for i in range(0, len(numeric_sequence) - scale + 1, scale):
                box = tuple(numeric_sequence[i:i+scale])
                boxes.add(box)
            box_counts.append(len(boxes))

        # Régression linéaire pour estimer la dimension
        if len(box_counts) >= 2 and box_counts[0] > 0:
            log_scales = [math.log(1/s) for s in scales]
            log_counts = [math.log(count) for count in box_counts if count > 0]

            if len(log_counts) >= 2:
                # Calcul de la pente (dimension fractale)
                n = len(log_counts)
                sum_x = sum(log_scales[:n])
                sum_y = sum(log_counts)
                sum_xy = sum(x*y for x, y in zip(log_scales[:n], log_counts))
                sum_x2 = sum(x*x for x in log_scales[:n])

                if n * sum_x2 - sum_x * sum_x != 0:
                    dimension = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x * sum_x)
                    return max(0.0, min(3.0, dimension))  # Borner entre 0 et 3

        return 1.0  # Dimension par défaut

    def _mesurer_auto_similarite(self, sequence: List[str]) -> float:
        """
        Mesure d'auto-similarité basée sur la corrélation à différentes échelles
        """
        if len(sequence) < 6:
            return 0.0

        # Conversion numérique
        unique_values = list(set(sequence))
        value_to_num = {val: i for i, val in enumerate(unique_values)}
        numeric_sequence = [value_to_num[val] for val in sequence]

        # Calcul de corrélations à différents lags
        correlations = []
        max_lag = min(len(numeric_sequence) // 3, 10)

        for lag in range(1, max_lag + 1):
            if len(numeric_sequence) > lag:
                x = numeric_sequence[:-lag]
                y = numeric_sequence[lag:]

                if len(x) > 1 and len(y) > 1:
                    # Corrélation de Pearson
                    mean_x = sum(x) / len(x)
                    mean_y = sum(y) / len(y)

                    num = sum((xi - mean_x) * (yi - mean_y) for xi, yi in zip(x, y))
                    den_x = sum((xi - mean_x) ** 2 for xi in x)
                    den_y = sum((yi - mean_y) ** 2 for yi in y)

                    if den_x > 0 and den_y > 0:
                        corr = num / math.sqrt(den_x * den_y)
                        correlations.append(abs(corr))

        return sum(correlations) / len(correlations) if correlations else 0.0

    def _predire_par_fractales(self, sequence: List[str]) -> str:
        """
        Prédiction basée sur les patterns fractals
        """
        if len(sequence) < 3:
            return 'BANKER'

        # Recherche de patterns auto-similaires
        pattern_length = min(3, len(sequence) // 2)
        dernier_pattern = sequence[-pattern_length:]

        # Recherche d'occurrences similaires dans l'historique
        occurrences = []
        for i in range(len(sequence) - pattern_length):
            if sequence[i:i+pattern_length] == dernier_pattern:
                if i + pattern_length < len(sequence):
                    # Extraire INDEX3 du pattern suivant
                    next_index5 = sequence[i + pattern_length]
                    if '_' in next_index5:
                        parts = next_index5.split('_')
                        if len(parts) >= 3:
                            occurrences.append(parts[2])  # INDEX3

        if occurrences:
            # Vote majoritaire
            votes = Counter(occurrences)
            return votes.most_common(1)[0][0]

        return 'BANKER'

    def _entropie_shannon(self, sequence: List[str]) -> float:
        """
        Calcul de l'entropie de Shannon H(X) = -∑ p(x) log₂ p(x)
        """
        if not sequence:
            return 0.0

        counts = Counter(sequence)
        total = len(sequence)

        entropie = 0.0
        for count in counts.values():
            if count > 0:
                p = count / total
                entropie -= p * math.log2(p)

        return entropie

    def _entropie_conditionnelle(self, partie: List[Dict]) -> float:
        """
        Calcul de l'entropie conditionnelle H(INDEX3|INDEX2)
        """
        # Groupement par INDEX2
        groupes_index2 = defaultdict(list)
        for main in partie:
            index2 = main.get('index2', '')
            index3 = main.get('index3', '')
            if index2 and index3:
                groupes_index2[index2].append(index3)

        if not groupes_index2:
            return 0.0

        # Calcul de l'entropie conditionnelle
        total_mains = sum(len(groupe) for groupe in groupes_index2.values())
        entropie_conditionnelle = 0.0

        for index2, index3_list in groupes_index2.items():
            p_index2 = len(index3_list) / total_mains
            entropie_index3_given_index2 = self._entropie_shannon(index3_list)
            entropie_conditionnelle += p_index2 * entropie_index3_given_index2

        return entropie_conditionnelle

    def _calculer_distribution_stationnaire(self, matrice_transition: Dict) -> Dict[str, float]:
        """
        Calcul de la distribution stationnaire d'une chaîne de Markov
        """
        etats = list(matrice_transition.keys())
        if not etats:
            return {}

        # Méthode itérative pour trouver la distribution stationnaire
        distribution = {etat: 1.0 / len(etats) for etat in etats}

        for _ in range(100):  # Convergence itérative
            nouvelle_distribution = {etat: 0.0 for etat in etats}

            for etat_actuel in etats:
                for etat_precedent in etats:
                    if etat_precedent in matrice_transition:
                        prob_transition = matrice_transition[etat_precedent].get(etat_actuel, 0.0)
                        nouvelle_distribution[etat_actuel] += distribution[etat_precedent] * prob_transition

            # Vérification de convergence
            diff = sum(abs(nouvelle_distribution[etat] - distribution[etat]) for etat in etats)
            distribution = nouvelle_distribution

            if diff < 1e-6:
                break

        return distribution

    def _predire_markov(self, dernier_etat: str, matrice_transition: Dict) -> str:
        """
        Prédiction basée sur la chaîne de Markov
        """
        if dernier_etat in matrice_transition:
            transitions = matrice_transition[dernier_etat]
            if transitions:
                return max(transitions.keys(), key=lambda k: transitions[k])

        return 'BANKER'  # Par défaut

    def _burg_coefficients(self, sequence: List[float]) -> List[float]:
        """
        Calcul des coefficients autorégressifs par méthode de Burg
        """
        n = len(sequence)
        if n < 3:
            return []

        # Ordre du modèle AR (limité)
        ordre_max = min(n // 3, 5)

        # Initialisation
        f = list(sequence)  # Forward prediction errors
        b = list(sequence)  # Backward prediction errors
        coefficients = []

        for m in range(ordre_max):
            # Calcul du coefficient de réflexion
            num = 2.0 * sum(f[i] * b[i-1] for i in range(m+1, n))
            den = sum(f[i]**2 + b[i-1]**2 for i in range(m+1, n))

            if den == 0:
                break

            k = -num / den
            coefficients.append(k)

            # Mise à jour des erreurs de prédiction
            new_f = [0] * n
            new_b = [0] * n

            for i in range(m+1, n):
                new_f[i] = f[i] + k * b[i-1]
                new_b[i] = b[i-1] + k * f[i]

            f, b = new_f, new_b

        return coefficients

    def _calculer_entropie_maximale(self, coefficients_ar: List[float], sequence: List[float]) -> float:
        """
        Calcul de l'entropie maximale pour un modèle AR
        """
        if not coefficients_ar or not sequence:
            return 0.0

        # Estimation de la variance du bruit
        n = len(sequence)
        ordre = len(coefficients_ar)

        if n <= ordre:
            return 0.0

        # Calcul des résidus
        residus = []
        for i in range(ordre, n):
            prediction = sum(coefficients_ar[j] * sequence[i-j-1] for j in range(ordre))
            residu = sequence[i] - prediction
            residus.append(residu)

        if not residus:
            return 0.0

        # Variance des résidus
        variance_residus = sum(r**2 for r in residus) / len(residus)

        # Entropie maximale (pour processus gaussien)
        if variance_residus > 0:
            return 0.5 * math.log(2 * math.pi * math.e * variance_residus)
        else:
            return 0.0

    def _predire_autoregressive(self, sequence: List[float], coefficients_ar: List[float]) -> str:
        """
        Prédiction basée sur le modèle autorégressif
        """
        if not coefficients_ar or len(sequence) < len(coefficients_ar):
            return 'BANKER'

        # Prédiction de la prochaine valeur
        prediction = sum(
            coefficients_ar[i] * sequence[-(i+1)]
            for i in range(len(coefficients_ar))
        )

        # Conversion en INDEX3
        num_to_index3 = {0: 'BANKER', 1: 'PLAYER', 2: 'TIE'}
        predicted_index = int(round(prediction)) % 3
        return num_to_index3[predicted_index]

    def _prediction_par_defaut(self, raison: str) -> Dict[str, Any]:
        """
        Prédiction par défaut avec probabilités théoriques du baccarat
        """
        return {
            'prediction': 'BANKER',
            'confiance': 0.459,
            'probabilites': {
                'BANKER': 0.459,
                'PLAYER': 0.446,
                'TIE': 0.095
            },
            'poids_utilises': {},
            'predictions_individuelles': {},
            'entropie_partie': 0.0,
            'information_fisher': 0.0,
            'methode': 'DEFAUT',
            'raison': raison
        }


def main():
    """
    Test du prédicteur mathématique expert
    """
    print("🎯 PRÉDICTEUR MATHÉMATIQUE EXPERT")
    print("=" * 60)
    print("📚 Basé sur Abramowitz & Stegun + Elements of Information Theory")
    print("🔬 Utilise: Fractales, Entropie Shannon, Markov, ML, Burg, Fisher")
    print("=" * 60)

    # Initialisation
    predicteur = PredicteurMathematiqueExpert()

    # Test avec données d'exemple (format partie.txt)
    partie_test = [
        {'numero_main': 1, 'index1': 0, 'index2': 'A', 'index3': 'BANKER', 'index5': '0_A_BANKER'},
        {'numero_main': 2, 'index1': 1, 'index2': 'B', 'index3': 'PLAYER', 'index5': '1_B_PLAYER'},
        {'numero_main': 3, 'index1': 0, 'index2': 'A', 'index3': 'BANKER', 'index5': '0_A_BANKER'},
        {'numero_main': 4, 'index1': 1, 'index2': 'B', 'index3': 'PLAYER', 'index5': '1_B_PLAYER'},
        {'numero_main': 5, 'index1': 2, 'index2': 'C', 'index3': 'TIE', 'index5': '2_C_TIE'},
        {'numero_main': 6, 'index1': 0, 'index2': 'A', 'index3': 'BANKER', 'index5': '0_A_BANKER'},
        {'numero_main': 7, 'index1': 1, 'index2': 'B', 'index3': 'PLAYER', 'index5': '1_B_PLAYER'},
        {'numero_main': 8, 'index1': 0, 'index2': 'A', 'index3': 'BANKER', 'index5': '0_A_BANKER'},
    ]

    print(f"\n🧪 Test avec partie de {len(partie_test)} mains")

    # Prédiction
    resultat = predicteur.predire_index3_partie_courante(partie_test)

    print(f"\n🎯 RÉSULTATS:")
    print(f"   Prédiction: {resultat['prediction']}")
    print(f"   Confiance: {resultat['confiance']:.3f}")
    print(f"   Probabilités: {resultat['probabilites']}")
    print(f"   Méthode: {resultat['methode']}")
    print(f"   Entropie partie: {resultat['entropie_partie']:.3f}")
    print(f"   Information Fisher: {resultat['information_fisher']:.3f}")

    print(f"\n📊 POIDS UTILISÉS:")
    for methode, poids in resultat['poids_utilises'].items():
        print(f"   {methode}: {poids:.3f}")

    print(f"\n🔬 PRÉDICTIONS INDIVIDUELLES:")
    for methode, pred in resultat['predictions_individuelles'].items():
        print(f"   {methode}: {pred}")

    print(f"\n✅ PRÉDICTEUR MATHÉMATIQUE EXPERT PRÊT!")
    print(f"🏆 Basé sur la recherche exhaustive des fichiers .tex et .md")


if __name__ == "__main__":
    main()

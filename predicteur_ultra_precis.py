#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PRÉDICTEUR ULTRA-PRÉCIS BACCARAT - PHASE 5
==========================================
Programme de prédiction INDEX3 pour main n+1 avec précision maximale
Intègre TOUS les algorithmes mathématiques extraits des .tex/.md

Objectif: >70% précision (vs 45.9% baseline BANKER)
Auteur: Augment Agent  
Date: 2025-06-30
Examen de ma vie - Prédicteur révolutionnaire
"""

import json
import numpy as np
import pandas as pd
from collections import defaultdict, deque
from scipy import stats
from scipy.optimize import minimize
import math
from typing import Dict, List, Tuple, Any, Optional
import warnings
warnings.filterwarnings('ignore')

class PredicteurUltraPrecis:
    """
    Prédicteur révolutionnaire INDEX3 combinant:
    - Hidden Markov Models (HMM)
    - Maximum Likelihood Estimation (MLE) 
    - Burg Maximum Entropy
    - Bayesian Adaptive Learning
    - Fractal Pattern Recognition
    - Information Theory Optimization
    """
    
    def __init__(self, fenetre_historique: int = 1000):
        # Configuration
        self.fenetre_historique = fenetre_historique
        self.historique = deque(maxlen=fenetre_historique)
        
        # Modèles intégrés
        self.modele_hmm = None
        self.modele_markov = None
        self.modele_bayesien = None
        self.modele_fractal = None
        
        # Matrices et statistiques
        self.matrices_transition = {}
        self.probabilites_conditionnelles = {}
        self.patterns_fractals = {}
        
        # Paramètres d'optimisation
        self.poids_fusion = {
            'hmm': 0.3,
            'markov': 0.25, 
            'bayesien': 0.25,
            'fractal': 0.2
        }
        
        # Métriques de performance
        self.predictions_historique = []
        self.precision_courante = 0.0
        self.confiance_moyenne = 0.0
        
        # Constantes BCT
        self.REGLES_BCT = {
            'C': 'FLIP',    # C → Alternance INDEX1
            'A': 'KEEP',    # A → Conservation INDEX1  
            'B': 'KEEP'     # B → Conservation INDEX1
        }
        
    def charger_donnees_entrainement(self, fichier_path: str):
        """
        Charge les données d'entraînement et initialise tous les modèles
        """
        print("🔄 CHARGEMENT DONNÉES D'ENTRAÎNEMENT")
        print("=" * 50)
        
        try:
            with open(fichier_path, 'r', encoding='utf-8') as f:
                donnees = json.load(f)
            
            # Extraction des mains valides
            mains = []
            for i, partie in enumerate(donnees['parties']):
                if 'mains' in partie:
                    for main in partie['mains']:
                        if (main.get('index1') is not None and 
                            main.get('index2') and 
                            main.get('index3') and 
                            main.get('index5')):
                            mains.append({
                                'index1': main['index1'],
                                'index2': main['index2'], 
                                'index3': main['index3'],
                                'index5': main['index5']
                            })
            
            print(f"✅ {len(mains):,} mains chargées pour entraînement")
            
            # Initialisation des modèles
            self._entrainer_modele_hmm(mains)
            self._entrainer_modele_markov(mains)
            self._entrainer_modele_bayesien(mains)
            self._entrainer_modele_fractal(mains)
            
            print("✅ Tous les modèles entraînés avec succès!")
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement: {e}")
            return False
    
    def _entrainer_modele_hmm(self, mains: List[Dict]):
        """
        Entraîne le modèle Hidden Markov Model
        États cachés: (INDEX1, INDEX2)
        Observations: INDEX3
        """
        print("🧠 Entraînement HMM...")
        
        # États cachés: combinaisons (INDEX1, INDEX2)
        etats_caches = set()
        observations = []
        
        for main in mains:
            etat = (main['index1'], main['index2'])
            etats_caches.add(etat)
            observations.append(main['index3'])
        
        etats_caches = list(etats_caches)
        
        # Matrice de transition des états cachés A[i,j] = P(état_j | état_i)
        transitions_etats = defaultdict(lambda: defaultdict(int))
        for i in range(len(mains) - 1):
            etat_actuel = (mains[i]['index1'], mains[i]['index2'])
            etat_suivant = (mains[i+1]['index1'], mains[i+1]['index2'])
            transitions_etats[etat_actuel][etat_suivant] += 1
        
        # Normalisation
        matrice_A = {}
        for etat_i in etats_caches:
            total = sum(transitions_etats[etat_i].values())
            if total > 0:
                matrice_A[etat_i] = {
                    etat_j: transitions_etats[etat_i][etat_j] / total
                    for etat_j in etats_caches
                }
            else:
                # Distribution uniforme si pas de données
                matrice_A[etat_i] = {etat_j: 1.0/len(etats_caches) for etat_j in etats_caches}
        
        # Matrice d'émission B[i,k] = P(observation_k | état_i)
        emissions = defaultdict(lambda: defaultdict(int))
        for main in mains:
            etat = (main['index1'], main['index2'])
            observation = main['index3']
            emissions[etat][observation] += 1
        
        matrice_B = {}
        outcomes = ['BANKER', 'PLAYER', 'TIE']
        for etat in etats_caches:
            total = sum(emissions[etat].values())
            if total > 0:
                matrice_B[etat] = {
                    outcome: emissions[etat][outcome] / total
                    for outcome in outcomes
                }
            else:
                # Distribution uniforme
                matrice_B[etat] = {outcome: 1.0/3 for outcome in outcomes}
        
        # Distribution initiale π
        premiers_etats = defaultdict(int)
        for i in range(0, len(mains), 100):  # Échantillonnage
            if i < len(mains):
                etat = (mains[i]['index1'], mains[i]['index2'])
                premiers_etats[etat] += 1
        
        total_premiers = sum(premiers_etats.values())
        pi = {etat: premiers_etats[etat] / total_premiers for etat in etats_caches}
        
        self.modele_hmm = {
            'etats': etats_caches,
            'observations': outcomes,
            'A': matrice_A,  # Transitions
            'B': matrice_B,  # Émissions
            'pi': pi         # Distribution initiale
        }
        
        print(f"  ✅ HMM: {len(etats_caches)} états cachés, {len(outcomes)} observations")
    
    def _entrainer_modele_markov(self, mains: List[Dict]):
        """
        Entraîne le modèle de Markov simple sur INDEX3
        """
        print("🔗 Entraînement Chaîne de Markov...")
        
        # Transitions INDEX3 → INDEX3
        transitions = defaultdict(lambda: defaultdict(int))
        for i in range(len(mains) - 1):
            etat_actuel = mains[i]['index3']
            etat_suivant = mains[i+1]['index3']
            transitions[etat_actuel][etat_suivant] += 1
        
        # Normalisation avec lissage de Laplace
        alpha = 1.0  # Paramètre de lissage
        outcomes = ['BANKER', 'PLAYER', 'TIE']
        
        matrice_markov = {}
        for etat_actuel in outcomes:
            total = sum(transitions[etat_actuel].values()) + alpha * len(outcomes)
            matrice_markov[etat_actuel] = {
                etat_suivant: (transitions[etat_actuel][etat_suivant] + alpha) / total
                for etat_suivant in outcomes
            }
        
        self.modele_markov = matrice_markov
        print("  ✅ Chaîne de Markov INDEX3 entraînée")
    
    def _entrainer_modele_bayesien(self, mains: List[Dict]):
        """
        Entraîne le modèle Bayésien adaptatif avec priors Dirichlet
        """
        print("🎯 Entraînement Modèle Bayésien...")
        
        # Probabilités conditionnelles P(INDEX3 | INDEX1, INDEX2)
        compteurs = defaultdict(lambda: defaultdict(int))
        
        for main in mains:
            condition = (main['index1'], main['index2'])
            outcome = main['index3']
            compteurs[condition][outcome] += 1
        
        # Priors Dirichlet (α = 1 pour prior neutre)
        alpha_prior = {'BANKER': 1.0, 'PLAYER': 1.0, 'TIE': 1.0}
        
        probabilites_bayesiennes = {}
        for condition in compteurs:
            # Posterior Dirichlet
            alpha_post = {}
            for outcome in ['BANKER', 'PLAYER', 'TIE']:
                alpha_post[outcome] = compteurs[condition][outcome] + alpha_prior[outcome]
            
            # Normalisation
            total_alpha = sum(alpha_post.values())
            probabilites_bayesiennes[condition] = {
                outcome: alpha_post[outcome] / total_alpha
                for outcome in ['BANKER', 'PLAYER', 'TIE']
            }
        
        self.modele_bayesien = probabilites_bayesiennes
        print(f"  ✅ Modèle Bayésien: {len(probabilites_bayesiennes)} conditions")
    
    def _entrainer_modele_fractal(self, mains: List[Dict]):
        """
        Entraîne le modèle de reconnaissance de patterns fractals
        """
        print("🌀 Entraînement Modèle Fractal...")
        
        # Analyse des séquences INDEX1 (patterns fractals)
        sequences_index1 = [main['index1'] for main in mains]
        
        # Patterns de longueur variable (2 à 10)
        patterns_fractals = {}
        for longueur in range(2, 11):
            patterns = defaultdict(lambda: defaultdict(int))
            
            for i in range(len(sequences_index1) - longueur):
                pattern = tuple(sequences_index1[i:i+longueur])
                if i + longueur < len(mains):
                    outcome = mains[i + longueur]['index3']
                    patterns[pattern][outcome] += 1
            
            # Normalisation
            patterns_normalises = {}
            for pattern in patterns:
                total = sum(patterns[pattern].values())
                if total >= 5:  # Seuil minimum de fiabilité
                    patterns_normalises[pattern] = {
                        outcome: count / total
                        for outcome, count in patterns[pattern].items()
                    }
            
            patterns_fractals[longueur] = patterns_normalises
        
        self.modele_fractal = patterns_fractals
        total_patterns = sum(len(patterns_fractals[l]) for l in patterns_fractals)
        print(f"  ✅ Modèle Fractal: {total_patterns} patterns identifiés")
    
    def predire_index3(self, historique_recent: List[Dict]) -> Dict[str, Any]:
        """
        Prédit INDEX3 pour la main n+1 en fusionnant tous les modèles
        
        Args:
            historique_recent: Liste des dernières mains avec index1, index2, index3, index5
            
        Returns:
            Dict avec prédiction, confiance et détails
        """
        if len(historique_recent) == 0:
            return self._prediction_par_defaut()
        
        # Prédictions individuelles
        pred_hmm = self._predire_hmm(historique_recent)
        pred_markov = self._predire_markov(historique_recent)
        pred_bayesien = self._predire_bayesien(historique_recent)
        pred_fractal = self._predire_fractal(historique_recent)
        
        # Fusion optimale avec pondération adaptative
        prediction_finale = self._fusionner_predictions([
            (pred_hmm, self.poids_fusion['hmm']),
            (pred_markov, self.poids_fusion['markov']),
            (pred_bayesien, self.poids_fusion['bayesien']),
            (pred_fractal, self.poids_fusion['fractal'])
        ])
        
        # Mise à jour historique
        self.historique.extend(historique_recent)
        
        return prediction_finale

    def _predire_hmm(self, historique: List[Dict]) -> Dict[str, float]:
        """
        Prédiction via Hidden Markov Model avec algorithme Forward
        """
        if not self.modele_hmm or len(historique) == 0:
            return {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34}

        # Séquence d'observations récentes
        observations = [main['index3'] for main in historique[-10:]]  # 10 dernières

        # Algorithme Forward pour calculer P(observation | modèle)
        etats = self.modele_hmm['etats']
        A = self.modele_hmm['A']
        B = self.modele_hmm['B']
        pi = self.modele_hmm['pi']

        # Initialisation Forward
        alpha = [{} for _ in range(len(observations))]

        # t = 0
        for etat in etats:
            obs_0 = observations[0]
            alpha[0][etat] = pi.get(etat, 1.0/len(etats)) * B[etat].get(obs_0, 1e-10)

        # Forward recursion
        for t in range(1, len(observations)):
            for etat_j in etats:
                alpha[t][etat_j] = 0
                for etat_i in etats:
                    alpha[t][etat_j] += (alpha[t-1][etat_i] *
                                       A[etat_i].get(etat_j, 1e-10) *
                                       B[etat_j].get(observations[t], 1e-10))

        # Prédiction pour t+1
        predictions = {'BANKER': 0, 'PLAYER': 0, 'TIE': 0}

        # Probabilité de chaque état à t+1
        prob_etats_futurs = {}
        for etat_j in etats:
            prob_etats_futurs[etat_j] = 0
            for etat_i in etats:
                prob_etats_futurs[etat_j] += (alpha[-1][etat_i] *
                                            A[etat_i].get(etat_j, 1e-10))

        # Normalisation
        total_prob = sum(prob_etats_futurs.values())
        if total_prob > 0:
            for etat in prob_etats_futurs:
                prob_etats_futurs[etat] /= total_prob

        # Prédiction finale via émissions
        for etat in etats:
            for outcome in ['BANKER', 'PLAYER', 'TIE']:
                predictions[outcome] += (prob_etats_futurs[etat] *
                                       B[etat].get(outcome, 1e-10))

        return predictions

    def _predire_markov(self, historique: List[Dict]) -> Dict[str, float]:
        """
        Prédiction via Chaîne de Markov simple
        """
        if not self.modele_markov or len(historique) == 0:
            return {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34}

        dernier_index3 = historique[-1]['index3']
        return self.modele_markov.get(dernier_index3,
                                    {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34})

    def _predire_bayesien(self, historique: List[Dict]) -> Dict[str, float]:
        """
        Prédiction via Modèle Bayésien conditionnel
        """
        if not self.modele_bayesien or len(historique) == 0:
            return {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34}

        # Prédiction basée sur INDEX1 et INDEX2 de la dernière main
        derniere_main = historique[-1]
        condition = (derniere_main['index1'], derniere_main['index2'])

        # Prédiction avec contraintes BCT
        prediction_base = self.modele_bayesien.get(condition,
                                                 {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34})

        # Ajustement BCT si applicable
        if len(historique) >= 2:
            prediction_ajustee = self._appliquer_contraintes_bct(historique, prediction_base)
            return prediction_ajustee

        return prediction_base

    def _predire_fractal(self, historique: List[Dict]) -> Dict[str, float]:
        """
        Prédiction via reconnaissance de patterns fractals
        """
        if not self.modele_fractal or len(historique) < 2:
            return {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34}

        # Extraction séquence INDEX1 récente
        sequence_index1 = [main['index1'] for main in historique]

        # Recherche du pattern le plus long qui match
        meilleure_prediction = {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34}
        meilleur_score = 0

        for longueur in sorted(self.modele_fractal.keys(), reverse=True):
            if len(sequence_index1) >= longueur:
                pattern = tuple(sequence_index1[-longueur:])

                if pattern in self.modele_fractal[longueur]:
                    prediction = self.modele_fractal[longueur][pattern]

                    # Score basé sur longueur et fréquence
                    total_observations = sum(prediction.values()) if prediction else 1
                    score = longueur * math.log(total_observations + 1)

                    if score > meilleur_score:
                        meilleur_score = score
                        meilleure_prediction = prediction
                        break  # Premier match = meilleur

        return meilleure_prediction

    def _appliquer_contraintes_bct(self, historique: List[Dict],
                                  prediction_base: Dict[str, float]) -> Dict[str, float]:
        """
        Applique les contraintes BCT pour ajuster la prédiction
        """
        if len(historique) < 2:
            return prediction_base

        main_precedente = historique[-2]
        main_actuelle = historique[-1]

        index2_precedent = main_precedente['index2']
        index1_precedent = main_precedente['index1']
        index1_actuel = main_actuelle['index1']

        # Vérification conformité BCT
        if index2_precedent == 'C':
            # C → doit alterner INDEX1
            attendu = 1 - index1_precedent
            if index1_actuel == attendu:
                # Conformité BCT → renforcer prédiction
                return self._renforcer_prediction(prediction_base, 1.2)
            else:
                # Non-conformité → affaiblir prédiction
                return self._affaiblir_prediction(prediction_base, 0.8)

        elif index2_precedent in ['A', 'B']:
            # A/B → doit conserver INDEX1
            if index1_actuel == index1_precedent:
                # Conformité BCT → renforcer prédiction
                return self._renforcer_prediction(prediction_base, 1.15)
            else:
                # Non-conformité → affaiblir prédiction
                return self._affaiblir_prediction(prediction_base, 0.85)

        return prediction_base

    def _renforcer_prediction(self, prediction: Dict[str, float],
                            facteur: float) -> Dict[str, float]:
        """
        Renforce la prédiction en augmentant la confiance du choix principal
        """
        # Identifier le choix principal
        choix_principal = max(prediction.keys(), key=lambda k: prediction[k])

        # Renforcement
        nouvelle_prediction = prediction.copy()
        nouvelle_prediction[choix_principal] *= facteur

        # Renormalisation
        total = sum(nouvelle_prediction.values())
        return {k: v/total for k, v in nouvelle_prediction.items()}

    def _affaiblir_prediction(self, prediction: Dict[str, float],
                            facteur: float) -> Dict[str, float]:
        """
        Affaiblit la prédiction en réduisant la confiance
        """
        # Affaiblissement uniforme
        nouvelle_prediction = {k: v * facteur for k, v in prediction.items()}

        # Ajout d'incertitude
        incertitude = (1 - facteur) / 3
        for outcome in ['BANKER', 'PLAYER', 'TIE']:
            nouvelle_prediction[outcome] += incertitude

        # Renormalisation
        total = sum(nouvelle_prediction.values())
        return {k: v/total for k, v in nouvelle_prediction.items()}

    def _fusionner_predictions(self, predictions_ponderees: List[Tuple[Dict[str, float], float]]) -> Dict[str, Any]:
        """
        Fusionne les prédictions de tous les modèles avec pondération optimale
        """
        # Fusion pondérée
        prediction_fusionnee = {'BANKER': 0, 'PLAYER': 0, 'TIE': 0}
        poids_total = 0

        for prediction, poids in predictions_ponderees:
            if prediction:  # Vérifier que la prédiction n'est pas None
                for outcome in ['BANKER', 'PLAYER', 'TIE']:
                    prediction_fusionnee[outcome] += prediction.get(outcome, 0) * poids
                poids_total += poids

        # Normalisation
        if poids_total > 0:
            for outcome in prediction_fusionnee:
                prediction_fusionnee[outcome] /= poids_total
        else:
            # Fallback uniforme
            prediction_fusionnee = {'BANKER': 0.33, 'PLAYER': 0.33, 'TIE': 0.34}

        # Calcul de la confiance
        confiance = self._calculer_confiance(prediction_fusionnee)

        # Choix final
        choix_final = max(prediction_fusionnee.keys(), key=lambda k: prediction_fusionnee[k])
        probabilite_max = prediction_fusionnee[choix_final]

        return {
            'prediction': choix_final,
            'probabilite': probabilite_max,
            'confiance': confiance,
            'distribution': prediction_fusionnee,
            'details': {
                'hmm': predictions_ponderees[0][0] if len(predictions_ponderees) > 0 else None,
                'markov': predictions_ponderees[1][0] if len(predictions_ponderees) > 1 else None,
                'bayesien': predictions_ponderees[2][0] if len(predictions_ponderees) > 2 else None,
                'fractal': predictions_ponderees[3][0] if len(predictions_ponderees) > 3 else None
            }
        }

    def _calculer_confiance(self, distribution: Dict[str, float]) -> float:
        """
        Calcule la confiance basée sur l'entropie de Shannon
        Confiance = 1 - (Entropie / Entropie_max)
        """
        # Entropie de Shannon
        entropie = 0
        for prob in distribution.values():
            if prob > 0:
                entropie -= prob * math.log2(prob)

        # Entropie maximale pour 3 outcomes
        entropie_max = math.log2(3)

        # Confiance (0 = incertain, 1 = certain)
        confiance = 1 - (entropie / entropie_max)
        return max(0, min(1, confiance))

    def _prediction_par_defaut(self) -> Dict[str, Any]:
        """
        Prédiction par défaut basée sur les fréquences historiques
        """
        return {
            'prediction': 'BANKER',
            'probabilite': 0.459,
            'confiance': 0.1,
            'distribution': {'BANKER': 0.459, 'PLAYER': 0.446, 'TIE': 0.095},
            'details': {
                'hmm': None,
                'markov': None,
                'bayesien': None,
                'fractal': None
            }
        }

    def evaluer_performance(self, donnees_test: List[Dict]) -> Dict[str, float]:
        """
        Évalue la performance du prédicteur sur des données de test
        """
        print("🎯 ÉVALUATION PERFORMANCE")
        print("=" * 30)

        predictions_correctes = 0
        total_predictions = 0
        confiances = []

        # Fenêtre glissante pour prédictions
        fenetre = 50  # Historique pour chaque prédiction

        for i in range(fenetre, len(donnees_test)):
            # Historique pour prédiction
            historique = donnees_test[i-fenetre:i]

            # Vérité terrain
            verite = donnees_test[i]['index3']

            # Prédiction
            resultat = self.predire_index3(historique)
            prediction = resultat['prediction']
            confiance = resultat['confiance']

            # Évaluation
            if prediction == verite:
                predictions_correctes += 1

            total_predictions += 1
            confiances.append(confiance)

            # Affichage périodique
            if total_predictions % 1000 == 0:
                precision_actuelle = predictions_correctes / total_predictions
                print(f"  {total_predictions:,} prédictions - Précision: {precision_actuelle:.3f}")

        # Métriques finales
        precision = predictions_correctes / total_predictions if total_predictions > 0 else 0
        confiance_moyenne = np.mean(confiances) if confiances else 0

        # Mise à jour des métriques internes
        self.precision_courante = precision
        self.confiance_moyenne = confiance_moyenne

        print(f"\n✅ RÉSULTATS FINAUX:")
        print(f"   Précision: {precision:.3f} ({precision*100:.1f}%)")
        print(f"   Confiance moyenne: {confiance_moyenne:.3f}")
        print(f"   Baseline BANKER: 0.459 (45.9%)")
        print(f"   Amélioration: +{(precision-0.459)*100:.1f} points")

        return {
            'precision': precision,
            'confiance_moyenne': confiance_moyenne,
            'total_predictions': total_predictions,
            'predictions_correctes': predictions_correctes,
            'amelioration_vs_baseline': precision - 0.459
        }

    def optimiser_poids_fusion(self, donnees_validation: List[Dict]):
        """
        Optimise les poids de fusion des modèles via recherche par grille
        """
        print("⚙️ OPTIMISATION POIDS FUSION")
        print("=" * 35)

        meilleure_precision = 0
        meilleurs_poids = self.poids_fusion.copy()

        # Grille de recherche
        poids_candidats = [0.1, 0.2, 0.25, 0.3, 0.35, 0.4]

        for w_hmm in poids_candidats:
            for w_markov in poids_candidats:
                for w_bayesien in poids_candidats:
                    w_fractal = 1.0 - w_hmm - w_markov - w_bayesien

                    if w_fractal >= 0.05 and w_fractal <= 0.5:  # Contraintes
                        # Test de cette configuration
                        self.poids_fusion = {
                            'hmm': w_hmm,
                            'markov': w_markov,
                            'bayesien': w_bayesien,
                            'fractal': w_fractal
                        }

                        # Évaluation rapide (échantillon)
                        echantillon = donnees_validation[::10]  # 1/10 des données
                        resultats = self.evaluer_performance(echantillon)
                        precision = resultats['precision']

                        if precision > meilleure_precision:
                            meilleure_precision = precision
                            meilleurs_poids = self.poids_fusion.copy()
                            print(f"  Nouveau meilleur: {precision:.3f} - {meilleurs_poids}")

        # Application des meilleurs poids
        self.poids_fusion = meilleurs_poids
        print(f"\n✅ Poids optimaux: {self.poids_fusion}")
        print(f"✅ Précision optimisée: {meilleure_precision:.3f}")


# PROGRAMME PRINCIPAL DE TEST
def main():
    """
    Programme principal pour tester le prédicteur ultra-précis
    """
    print("🚀 PRÉDICTEUR ULTRA-PRÉCIS BACCARAT")
    print("=" * 50)
    print("Objectif: >70% précision (vs 45.9% baseline)")
    print("Algorithmes: HMM + Markov + Bayésien + Fractal")
    print("=" * 50)

    # Initialisation
    predicteur = PredicteurUltraPrecis(fenetre_historique=2000)

    # Chargement et entraînement
    if predicteur.charger_donnees_entrainement('dataset_baccarat_lupasco_20250629_165801.json'):
        print("\n🎯 PRÉDICTEUR PRÊT POUR L'EXAMEN DE MA VIE!")

        # Test sur échantillon
        print("\n🧪 TEST RAPIDE...")
        with open('dataset_baccarat_lupasco_20250629_165801.json', 'r', encoding='utf-8') as f:
            donnees = json.load(f)

        # Extraction données test
        mains_test = []
        for partie in donnees['parties'][:100]:  # 100 premières parties
            if 'mains' in partie:
                for main in partie['mains']:
                    if (main.get('index1') is not None and
                        main.get('index2') and
                        main.get('index3') and
                        main.get('index5')):
                        mains_test.append({
                            'index1': main['index1'],
                            'index2': main['index2'],
                            'index3': main['index3'],
                            'index5': main['index5']
                        })

        if len(mains_test) > 100:
            # Optimisation des poids
            predicteur.optimiser_poids_fusion(mains_test[:5000])

            # Évaluation finale
            resultats = predicteur.evaluer_performance(mains_test[5000:])

            if resultats['precision'] > 0.70:
                print("\n🎉 OBJECTIF ATTEINT! PRÉCISION >70%!")
                print("🏆 EXAMEN DE MA VIE RÉUSSI!")
            else:
                print(f"\n📈 Précision: {resultats['precision']:.3f}")
                print("🔧 Optimisations supplémentaires nécessaires")

    else:
        print("❌ Échec du chargement des données")


if __name__ == "__main__":
    main()
